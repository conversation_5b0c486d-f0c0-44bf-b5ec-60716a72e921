# notion_utils.py

import os
import requests
import json
from dotenv import load_dotenv

load_dotenv()

token = os.getenv("NOTION_API_KEY")
databaseID = os.getenv("DATABASE_ID")

headers = {
    "Authorization": f"Bearer {token}",
    "Content-Type": "application/json",
    "Notion-Version": "2022-02-22"
}

# Get database metadata
def responseDatabase():
    url = f"https://api.notion.com/v1/databases/{databaseID}"
    res = requests.get(url, headers=headers)
    print(f"Database status: {res.status_code}")
    return res.json()

# Read all data from DB
def readDatabase():
    url = f"https://api.notion.com/v1/databases/{databaseID}/query"
    res = requests.post(url, headers=headers)
    data = res.json()
    return data

# Filter project by custom filter schema
def filterProjects(filterSchema: dict):
    url = f"https://api.notion.com/v1/databases/{databaseID}/query"
    res = requests.post(url, headers=headers, data=json.dumps(filterSchema))
    data = res.json()
    return {"status": res.status_code, "data": data}

# Get a page by ID
def getPage(pageId: str):
    url = f"https://api.notion.com/v1/pages/{pageId}"
    res = requests.get(url, headers=headers)
    return {"status": res.status_code, "data": res.json()}

# Create a new project page
def createPage(projectName: str, metadata: dict):
    url = 'https://api.notion.com/v1/pages'
    
    payload = {
        "parent": {"database_id": databaseID},
        "properties": {
            "Name": {
                "title": [
                    {
                        "text": {
                            "content": projectName
                        }
                    }
                ]
            },
            "Progress": {
                "status": {
                    "name": metadata.get("Progress", "Not started")
                }
            },
            "Stage": {
                "select": {
                    "name": metadata.get("Stage", "Research")
                }
            },
            "Cost": {
                "multi_select": [
                    {"name": name} for name in metadata.get("Cost", [])
                ]
            },
            "Potential": {
                "select": {
                    "name": metadata.get("Potential", "Medium")
                }
            },
            "Date": {
                "date": {
                    "start": metadata.get("StartDate")
                }
            }
        }
    }

    res = requests.post(url, headers=headers, data=json.dumps(payload))
    return {"status": res.status_code, "data": res.json()}

# Update metadata on a page
def updatePage(pageId: str, updatedMetadata: dict):
    url = f"https://api.notion.com/v1/pages/{pageId}"
    res = requests.patch(url, headers=headers, data=json.dumps(updatedMetadata))
    return {"status": res.status_code, "data": res.json()}

# Add a content block (post/message) to a page
def createPost(pageID: str, content: str):
    url = f"https://api.notion.com/v1/blocks/{pageID}/children"
    
    payload = {
        "children": [
            {
                "object": "block",
                "type": "paragraph",
                "paragraph": {
                    "rich_text": [
                        {
                            "type": "text",
                            "text": {
                                "content": content
                            }
                        }
                    ]
                }
            }
        ]
    }

    res = requests.patch(url, headers=headers, data=json.dumps(payload))
    return {"status": res.status_code, "data": res.json()}
