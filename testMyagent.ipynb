from dotenv import load_dotenv
import requests
import os
import json

load_dotenv(".env", override=True)


token = os.getenv("NOTION_API_KEY")
databaseID = os.getenv("DATABASE_ID")

headers = {
    "Authorization": f"Bearer {token}",
    "Content-Type": "application/json",
    "Notion-Version": "2022-02-22"
}

from langchain.chat_models import init_chat_model
llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")

from langgraph.graph import MessagesState
from typing import Literal
from langgraph.types import Command

class State(MessagesState):
    # We can add a specific key to our state for the email input
    post_input: dict
    classification_decision: Literal["ignore", "notify", "take_action"]


from pydantic import BaseModel, Field

class ProjectSchema(BaseModel):
    """Analyze the project"""
    
    reasoning: str = Field(
        description="Step-by-step reasoning behind the classification."
    )
    project_name: str = Field(
        description="The project name."
    )
    funding: int = Field(
        description="Function of the projet "
    )
    Rating: int = Field(
        description="Rating of the project"
    )
    project_type: str = Field(
        description="Type of the project"
    )
    project_category : str = Field(
        description="Category of the project"
    )
    

class RouterSchema(BaseModel):
    """ Analyze the post and route it according to its content."""

    reasoning: str = Field(
        description="Step-by-step reasoning behind the classification."
    )
    classification: Literal["ignore", "notify", "take_action"] = Field(
        description="The classification of an post: 'ignore' for irrelevant post, "
        "'notify' for important information that doesn't require response, "
        "'take_action' for post that requires a action like to create a task, create a page",
    )
    
    
llm_router = llm.with_structured_output(RouterSchema)

from TelegramAi.testPrompt import triage_system_prompt, default_background, default_triage_instructions , triage_user_prompt, AGENT_TOOLS_PROMPT, default_response_preferences, agent_system_prompt
from rich.markdown import Markdown
from langgraph.graph import END

def triage_router(state: State) -> Command[Literal["action_agent", "__end__"]]:
    """Analyze post content to decide if we should create a task, notify, or ignore."""
    
    # Parse the post input
    post_input = state["post_input"]
    text = post_input.get("text", "")
    metadata = post_input.get("metadata", {})
    system_prompt = triage_system_prompt.format(
        triage_instructions=default_triage_instructions
    )
    
    user_prompt = triage_user_prompt.format(
        metadata=metadata,
        post_thread=text
    )
    
    # Run the router LLM
    result = llm_router.invoke(
        [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": text},
        ]
    )
    print(result)
    # Decision
    if result.classification == "take_action":
        print("📧 Classification: Take Action - This post requires to create a task")
        goto = "action_agent"
        update = {
            "messages": [
                {
                    "role": "user",
                    "content": f"Take action for this post: \n\n{user_prompt}",
                }
            ],
            "classification_decision": result.classification,
        }
    
    elif result.classification == "ignore":
        print("🚫 Classification: IGNORE - This post can be safely ignored")
        goto = END
        update =  {
            "classification_decision": result.classification,
        }
    
    elif result.classification == "notify":
        print("🔔 Classification: NOTIFY - This post contains important information")
        goto = END
        update = {
            "classification_decision": result.classification,
        }
    else:
        raise ValueError(f"Invalid classification: {result.classification}")
    return Command(goto=goto, update=update)


msg = """
🔈 Cysic Airdrop — Social Task Update ✔️

⏱️ In 5 hours: They remove the Twitter task
🗓 In 17 hours: auto-complete any stuck Social Tasks for affected users

🔗 Link : https://app.cysic.xyz

If you Haven't Joined Join now Drop your Code Below - https://x.com/cryptoimsny2/status/1950840964840337832

——

🆕 New users can aslo join

"""

from langchain.tools import tool
from pydantic import BaseModel
from typing import Optional, List, Dict

@tool
def notify_user(message: str, urgency: str) -> str:
    """Notify the user about important updates or task creation."""
    return f"User notified with message: {message} and urgency: {urgency}"

@tool
def read_database_metadata():
    """Get database schema with all properties and their types. Call this FIRST."""
    url = f"https://api.notion.com/v1/databases/{databaseID}"  # Fixed endpoint
    res = requests.get(url, headers=headers)
    data = res.json()
    
    # Extract and format property information for AI understanding
    if res.status_code == 200 and 'properties' in data:
        formatted_info = {
            "database_id": data.get("id"),
            "title": data.get("title", [{}])[0].get("plain_text", ""),
            "properties": {}
        }
        
        for prop_name, prop_data in data["properties"].items():
            prop_type = prop_data.get("type")
            formatted_info["properties"][prop_name] = {
                "type": prop_type,
                "id": prop_data.get("id")
            }
            
            # Add options for select/multi_select
            if prop_type in ["select", "multi_select"] and prop_type in prop_data:
                options = prop_data[prop_type].get("options", [])
                formatted_info["properties"][prop_name]["options"] = [
                    opt["name"] for opt in options
                ]
        
        return {"status": res.status_code, "schema": formatted_info, "raw": data}
    
    return {"status": res.status_code, "data": data}

@tool
def retrive_database_data(
    filter: Optional[Dict] = None,
    sorts: Optional[List[Dict]] = None,
    start_cursor: Optional[str] = None,
    page_size: Optional[int] = 100
):
    """
    Read data from the notion database with advanced filtering and sorting.
    
    Args:
        filter: Filter conditions (e.g., {"property": "Status", "select": {"equals": "Done"}})
        sorts: Sort criteria (e.g., [{"property": "Created", "direction": "descending"}])
        start_cursor: Pagination cursor for next page
        page_size: Number of items per page (max 100)
    """
    payload = {}
    if filter:
        payload["filter"] = filter
    if sorts:
        payload["sorts"] = sorts
    if start_cursor:
        payload["start_cursor"] = start_cursor
    if page_size:
        payload["page_size"] = page_size
    
    url = f"https://api.notion.com/v1/databases/{databaseID}/query"
    res = requests.post(url, headers=headers, json=payload)
    return {"status": res.status_code, "data": res.json()}

@tool
def create_page(
    title: str,
    properties: Optional[Dict] = None,
    children: Optional[List[Dict]] = None
):
    """
    Create a new page in the notion database.
    
    Args:
        title: Page title
        properties: Additional properties (e.g., {"Status": {"select": {"name": "In Progress"}}})
        children: Initial content blocks
    """
    # Build the title property
    title_property = {
        "title": [{"text": {"content": title}}]
    }
    
    # Merge with additional properties
    all_properties = {**title_property, **(properties or {})}
    
    payload = {
        "parent": {"database_id": databaseID},
        "properties": all_properties
    }
    
    if children:
        payload["children"] = children
    
    url = 'https://api.notion.com/v1/pages'
    res = requests.post(url, headers=headers, json=payload)
    return {"status": res.status_code, "data": res.json()}

@tool
def upadate_page(
    page_id: str,
    properties: Optional[Dict] = None,
    archived: Optional[bool] = None,
    cover: Optional[Dict] = None,
    icon: Optional[Dict] = None
):
    """
    Update a page in the notion database.
    
    Args:
        page_id: ID of the page to update
        properties: Properties to update
        archived: Whether to archive the page
        cover: Cover image object
        icon: Icon object
    """
    payload = {}
    if properties:
        payload["properties"] = properties
    if archived is not None:
        payload["archived"] = archived
    if cover:
        payload["cover"] = cover
    if icon:
        payload["icon"] = icon
    
    url = f"https://api.notion.com/v1/pages/{page_id}"
    res = requests.patch(url, headers=headers, json=payload)
    
    # Enhanced error handling
    if res.status_code >= 400:
        error_data = res.json()
        return {
            "status": res.status_code,
            "error": error_data.get("message", "Unknown error"),
            "code": error_data.get("code"),
            "data": error_data
        }
    
    return {"status": res.status_code, "data": res.json()}

@tool
def create_block(page_id: str, children: List[Dict]):
    """Create new blocks in a notion page"""
    url = f"https://api.notion.com/v1/blocks/{page_id}/children"
    payload = {"children": children}
    
    res = requests.patch(url, headers=headers, json=payload)
    return {"status": res.status_code, "data": res.json()}

@tool
def update_block(block_id: str, payload: dict):
    """Update a block in the notion page."""
    url = f"https://api.notion.com/v1/blocks/{block_id}"
    res = requests.patch(url, headers=headers, json=payload)
    return {"status": res.status_code, "data": res.json()}

@tool
def retreve_block_children(
    block_id: str,
    start_cursor: Optional[str] = None,
    page_size: int = 100
):
    """Retrieve all children of a block with pagination"""
    url = f"https://api.notion.com/v1/blocks/{block_id}/children"
    params = {"page_size": page_size}
    if start_cursor:
        params["start_cursor"] = start_cursor
    
    res = requests.get(url, headers=headers, params=params)
    return {"status": res.status_code, "data": res.json()}

@tool
def search_pages(
    query: Optional[str] = None,
    filter_value: Optional[str] = None,
    sort_direction: str = "descending",
    sort_timestamp: str = "last_edited_time",
    start_cursor: Optional[str] = None,
    page_size: int = 100
):
    """
    Search across all pages and databases.
    
    Args:
        query: Text to search for
        filter_value: Filter by object type ("page" or "database")
        sort_direction: "ascending" or "descending"
        sort_timestamp: "last_edited_time" or "created_time"
        start_cursor: Pagination cursor
        page_size: Results per page
    """
    payload = {
        "sort": {
            "direction": sort_direction,
            "timestamp": sort_timestamp
        },
        "page_size": page_size
    }
    
    if query:
        payload["query"] = query
    if filter_value:
        payload["filter"] = {"value": filter_value, "property": "object"}
    if start_cursor:
        payload["start_cursor"] = start_cursor
    
    url = "https://api.notion.com/v1/search"
    res = requests.post(url, headers=headers, json=payload)
    return {"status": res.status_code, "data": res.json()}

@tool
def create_page_with_validation(payload: dict):
    """Create a new page with schema validation."""
    # First get database metadata to validate
    metadata_result = read_database_metadata()
    if metadata_result["status"] != 200:
        return {"error": "Could not fetch database schema", "status": 400}
    
    schema = metadata_result["schema"]["properties"]
    
    # Validate payload against schema
    validated_payload = {}
    for prop_name, prop_value in payload.items():
        if prop_name not in schema:
            return {"error": f"Property '{prop_name}' not found in database schema", "status": 400}
        validated_payload[prop_name] = prop_value
    
    # Create the page
    full_payload = {
        "parent": {"database_id": databaseID},
        "properties": validated_payload
    }
    
    url = 'https://api.notion.com/v1/pages'
    res = requests.post(url, headers=headers, json=full_payload)
    return {"status": res.status_code, "data": res.json()}

@tool
def create_task_block(page_id: str, task_name: str, checked: bool = False):
    """Create a to-do block in a page."""
    payload = {
        "children": [{
            "object": "block",
            "type": "to_do",
            "to_do": {
                "rich_text": [{"type": "text", "text": {"content": task_name}}],
                "checked": checked,
                "color": "default"
            }
        }]
    }
    
    url = f"https://api.notion.com/v1/blocks/{page_id}/children"
    res = requests.patch(url, headers=headers, json=payload)
    return {"status": res.status_code, "data": res.json()}
@tool
class Done(BaseModel):
    """Task has been completed."""
    done: bool
      
      


tools = [
    create_page, 
    notify_user, 
    read_database_metadata, 
    retrive_database_data,  # Fixed typo
    create_block, 
    update_block, 
    retreve_block_children,  # Fixed typo
    upadate_page,  # Fixed typo
    search_pages,  # Added missing tool
    create_page_with_validation,  # Added missing tool
    create_task_block,  # Added missing tool
    Done
]
tools_by_name = {tool.name: tool for tool in tools}
model_with_tools = llm.bind_tools(tools, tool_choice="any", parallel_tool_calls=False)

def llm_call(state: State):
    """LLM decides whether to call a tool or not"""
    output = {
        "messages": [
            model_with_tools.invoke(
                [
                    {"role": "system", "content": agent_system_prompt.format(
                        tools_prompt=AGENT_TOOLS_PROMPT,
                        background=default_background,
                        response_preferences=default_response_preferences,
                        )
                    },
                    
                ]
                + state["messages"]
            )
        ]
    }
    return output


Markdown(agent_system_prompt.format(
    tools_prompt=AGENT_TOOLS_PROMPT,
    background=default_background,
    response_preferences=default_response_preferences,
))

llm_call({"messages": [{"role": "user", "content": "Hello"}]})

def tool_handler(state: State):
    """Performs the tool call."""

    # List for tool messages
    result = []
    
    # Iterate through tool calls
    for tool_call in state["messages"][-1].tool_calls:
        # Get the tool
        tool = tools_by_name[tool_call["name"]]
        # Run it
        observation = tool.invoke(tool_call["args"])
        # Create a tool message
        result.append({"role": "tool", "content": observation, "tool_call_id": tool_call["id"]})
    
    # Add it to our messages
    return {"messages": result}


def should_continue(state: State) -> Literal["tool_handler", "__end__"]:
    """Route to tool handler, or end if Done tool called."""

    # Get the last message
    messages = state["messages"]
    last_message = messages[-1]

    # Check if there are tool calls
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        for tool_call in last_message.tool_calls:
            if tool_call["name"] == "Done":
                print("✅ Done tool called - ending workflow")
                return END
            else:
                print(f"🔧 Tool call found: {tool_call['name']} - routing to tool_handler")
                return "tool_handler"
    else:
        # No tool calls, end the workflow
        print("🏁 No tool calls found - ending workflow")
        return END



from langgraph.graph import StateGraph, START, END
from email_assistant.utils import show_graph

# Build workflow
overall_workflow = StateGraph(State)

# Add nodes
overall_workflow.add_node("llm_call", llm_call)
overall_workflow.add_node("tool_handler", tool_handler)

# Add edges
overall_workflow.add_edge(START, "llm_call")
overall_workflow.add_conditional_edges(
    "llm_call",
    should_continue,
    {
        "tool_handler": "tool_handler",
        END: END,
    },
)
overall_workflow.add_edge("tool_handler", "llm_call")

# Compile the agent
agent = overall_workflow.compile()

overall_workflow = (
    StateGraph(State)
    .add_node(triage_router)
    .add_node("action_agent", agent)
    .add_edge(START, "triage_router")
).compile()

from IPython.display import Image, display

try:
    display(Image(overall_workflow.get_graph().draw_mermaid_png()))
except Exception:
    # This requires some extra dependencies and is optional
    pass

post = {"text": """

 Opensea Airdrop — New tasks are Live ✔️

🔗 Link — https://opensea.io/rewards

1️⃣. Buy NFT from a Verified Collection on any chain [ Minimum $5 USD ]
✨  50 XP 

🖼 NFT Collection [ Base Chain - Cost $17 ]  — https://opensea.io/collection/dxterminal

🟢 Buy this NFT 
🟢 Claim XP 
🟢 Sell NFT 
✅ Done 

———

❗️ Once you Complete 1st Task and Claim XP now you can Complete 2nd Task

2️⃣. Go to a Gaming NFT Branded Collection Page
✨  50 XP 

🟢 Click on the Gaming NFT [ Dxterminal NFT ]
🟢 Go to the About Section and Scroll Down until you See Complete Voyage
❗️ If you are not able to click on Complete Voyage Try to Zoom out a little on Web ]
🟢 Claim XP 
✅ Done 

———

3️⃣. Complete a token swap on any chain [ Minimum $10 USD ]
✨  100 XP 

🟢 Swap on any chain [ Minimum $10 USD ] 
➡️ Do it on Base or unichain [ For Future Airdrop ]
🟢 Claim XP 
🟢 Sell your Tokens 
✅ Done

———

🆕 Now for New users
""",
"metadata": {}}

post = {"text": "Give me what parameter have in my notion database and also a important that Opensea airdrop have new task go to there website and complete the verification", "metadata": {}}

config = {"configurable": {"thread_id": "1"}}

response = overall_workflow.invoke({"post_input": post}, config)


for m in response["messages"]:
    m.pretty_print()

from main import get_client, TeleBot
import asyncio
from typing import Optional, List, Dict, Any
from datetime import datetime
import re

def extract_telegram_message(message) -> Dict[str, Any]:
    # Extract the main text
    text = message.message or ""
    
    # Build metadata dictionary with all the extra stuff
    metadata = {
        "date": message.date,
        "is_forwarded": bool(message.fwd_from),
        "is_post": message.post,
        "from_channel": str(message.peer_id.channel_id) if message.peer_id else None,
        "message_id": message.id,
        "all_links": re.findall(r'https?://\S+', text),
    }
    
    # Add media/webpage info if it exists
    if message.media and getattr(message.media, 'webpage', None):
        webpage = message.media.webpage
        metadata.update({
            "media_url": getattr(webpage, 'url', None),
            "link_preview_title": getattr(webpage, 'title', None),
            "link_preview_description": getattr(webpage, 'description', None),
            "link_preview_site": getattr(webpage, 'site_name', None),
        })
    
    # Add forward info if message is forwarded
    if message.fwd_from:
        metadata["forward_info"] = {
            "from_id": str(message.fwd_from.from_id) if message.fwd_from.from_id else None,
            "from_name": message.fwd_from.from_name,
            "date": message.fwd_from.date,
        }
    
    # Extract bold entities
    """
    bold_entities = []
    if hasattr(message, 'entities') and message.entities:
        for entity in message.entities:
            if entity.__class__.__name__ == "MessageEntityBold":
                offset = entity.offset
                length = entity.length
                bold_entities.append(text[offset:offset+length])
    
    if bold_entities:
        metadata["bold_entities"] = bold_entities
    """
    
    # Return as plain dict
    return {
        "text": text,
        "metadata": metadata
    }

